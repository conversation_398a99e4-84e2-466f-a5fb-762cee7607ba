"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionCreateEventHandler = void 0;
const base_1 = require("./base");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const SuggestionService_1 = require("../services/suggestion/SuggestionService");
const electionButtonHandler_1 = require("../handlers/electionButtonHandler");
const pollButtonHandler_1 = require("../handlers/pollButtonHandler");
const leaderboardButtonHandler_1 = require("../handlers/leaderboardButtonHandler");
const helpButtonHandler_1 = require("../handlers/helpButtonHandler");
class InteractionCreateEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'interactionCreate');
        this.name = 'interactionCreate';
        this.electionButtonHandler = new electionButtonHandler_1.ElectionButtonHandler(app);
        this.pollButtonHandler = new pollButtonHandler_1.PollButtonHandler(app);
        this.leaderboardButtonHandler = new leaderboardButtonHandler_1.LeaderboardButtonHandler(app);
        this.suggestionService = new SuggestionService_1.SuggestionService(app);
    }
    async execute(interaction) {
        try {
            if (interaction.isChatInputCommand()) {
                await this.handleChatInputCommand(interaction);
            }
            else if (interaction.isButton()) {
                await this.handleButtonInteraction(interaction);
            }
            else if (interaction.isModalSubmit()) {
                await this.handleModalSubmit(interaction);
            }
        }
        catch (error) {
            this.handleError(error, {
                interactionType: interaction.type,
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
            });
        }
    }
    async handleChatInputCommand(interaction) {
        const command = this.app.client.commands.get(interaction.commandName);
        if (!command) {
            this.logger.warn(`[InteractionCreate] Unknown command: ${interaction.commandName}`);
            return;
        }
        try {
            this.logExecution(`Executing command: ${interaction.commandName}`, {
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
                channelId: interaction.channel?.id,
            });
            const context = {
                interaction,
                client: this.app.client,
                guild: interaction.guild,
                member: interaction.member,
                logger: this.logger,
            };
            await command.execute(interaction);
        }
        catch (error) {
            await (0, errorHandler_1.handleCommandError)(interaction, error);
        }
    }
    async handleButtonInteraction(interaction) {
        try {
            const { customId } = interaction;
            this.logExecution(`Handling button interaction: ${customId}`, {
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
                channelId: interaction.channel?.id,
            });
            const context = {
                interaction,
                client: this.app.client,
                guild: interaction.guild,
                member: interaction.member,
                logger: this.logger,
            };
            if (customId === 'quick_balance' || customId === 'salary_balance') {
                await this.handleQuickAction(interaction, 'balance');
            }
            else if (customId === 'quick_leaderboard' || customId === 'salary_leaderboard') {
                await this.handleQuickAction(interaction, 'leaderboard');
            }
            else if (customId === 'quick_roles' || customId === 'salary_roles') {
                await this.handleQuickAction(interaction, 'roles');
            }
            else if (customId.startsWith('buy_role_')) {
                await this.handleRoleAchievementInfo(interaction, customId);
            }
            else if (customId === 'announce_confirm') {
                await this.handleAnnouncementConfirm(interaction);
            }
            else if (customId === 'announce_cancel') {
                await this.handleAnnouncementCancel(interaction);
            }
            else if (customId.startsWith('help_')) {
                const timestamp = new Date().toISOString();
                console.log(`[INTERACTION DEBUG ${timestamp}] ===== ROUTING HELP BUTTON =====`);
                console.log(`[INTERACTION DEBUG ${timestamp}] CustomId: "${customId}"`);
                console.log(`[INTERACTION DEBUG ${timestamp}] User: ${interaction.user.tag} (${interaction.user.id})`);
                console.log(`[INTERACTION DEBUG ${timestamp}] Guild: ${interaction.guild?.name} (${interaction.guild?.id})`);
                console.log(`[INTERACTION DEBUG ${timestamp}] Routing to handleHelpButton...`);
                try {
                    await (0, helpButtonHandler_1.handleHelpButton)(interaction);
                    console.log(`[INTERACTION DEBUG ${timestamp}] ✅ handleHelpButton completed successfully`);
                }
                catch (error) {
                    console.error(`[INTERACTION DEBUG ${timestamp}] ❌ handleHelpButton failed:`, error);
                    throw error;
                }
            }
            else if (customId.startsWith('trade_')) {
                await this.handleTradeButton(interaction, customId);
            }
            else if (customId.startsWith('election_')) {
                await this.electionButtonHandler.handleElectionButton(interaction, customId);
            }
            else if (customId.startsWith('poll_')) {
                await this.pollButtonHandler.handlePollButton(interaction);
            }
            else if (customId.startsWith('leaderboard_')) {
                await this.leaderboardButtonHandler.handleLeaderboardButton(interaction, customId);
            }
            else if (customId.startsWith('suggestion_')) {
                await this.handleSuggestionButton(interaction, customId);
            }
            else {
                await interaction.reply({
                    content: 'This button interaction is not yet implemented.',
                    ephemeral: true
                });
            }
        }
        catch (error) {
            await (0, errorHandler_1.handleButtonError)(interaction, error);
        }
    }
    async handleModalSubmit(interaction) {
        try {
            const { customId } = interaction;
            if (customId.startsWith('suggestion_edit_modal_')) {
                await this.handleSuggestionEditModal(interaction, customId);
            }
            else {
                await interaction.reply({
                    content: 'This modal interaction is not yet implemented.',
                    ephemeral: true
                });
            }
        }
        catch (error) {
            this.logger.error('[InteractionCreate] Error handling modal submit', {
                error,
                customId: interaction.customId,
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
            });
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: 'An error occurred while processing your request.',
                    ephemeral: true
                });
            }
        }
    }
    async handleQuickAction(interaction, commandName) {
        try {
            const guildId = interaction.guild?.id;
            if (!guildId) {
                await interaction.reply({
                    content: 'This action can only be used in a server.',
                    ephemeral: true
                });
                return;
            }
            switch (commandName) {
                case 'balance':
                    await this.handleBalanceQuickAction(interaction, guildId);
                    break;
                case 'leaderboard':
                    await this.handleLeaderboardQuickAction(interaction, guildId);
                    break;
                case 'roles':
                    await this.handleRolesQuickAction(interaction, guildId);
                    break;
                default:
                    await interaction.reply({
                        content: `Quick action "${commandName}" not implemented.`,
                        ephemeral: true
                    });
            }
        }
        catch (error) {
            this.logger.error(`Error handling quick action ${commandName}:`, error);
            await interaction.reply({
                content: 'An error occurred while processing your request.',
                ephemeral: true
            });
        }
    }
    async handleBalanceQuickAction(interaction, guildId) {
        const { ensureUser } = await Promise.resolve().then(() => __importStar(require('../services/economyService')));
        const { formatServerCoins, createServerEconomyEmbed, createQuickActionButtons, EMOJIS } = await Promise.resolve().then(() => __importStar(require('../utils/embedBuilder')));
        const discordId = interaction.user.id;
        const user = await ensureUser(discordId, guildId);
        const formattedBalance = await formatServerCoins(guildId, user.balance);
        const embed = await createServerEconomyEmbed(guildId, 'Your Balance');
        embed.setDescription(`${formattedBalance}\n\n${EMOJIS.ECONOMY.SPARKLES} *Keep earning to climb the leaderboard!*`)
            .addFields({
            name: `${EMOJIS.ECONOMY.BANK} Account Status`,
            value: user.balance >= 1000 ?
                `${EMOJIS.SUCCESS.CROWN} **Premium Member**` :
                `${EMOJIS.ECONOMY.CHART} **Growing Account**`,
            inline: true
        }, {
            name: `${EMOJIS.MISC.CALENDAR} Last Updated`,
            value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
            inline: true
        })
            .setFooter({
            text: 'Use /help to see ways to earn more coins!'
        });
        const buttons = createQuickActionButtons();
        await interaction.reply({
            embeds: [embed],
            components: [buttons],
            ephemeral: false
        });
    }
    async handleLeaderboardQuickAction(interaction, guildId) {
        const { LeaderboardManager } = await Promise.resolve().then(() => __importStar(require('../services/economy/managers/LeaderboardManager')));
        const { createServerEconomyEmbed, formatServerCoins, createNavigationButtons, EMOJIS, COLORS } = await Promise.resolve().then(() => __importStar(require('../utils/embedBuilder')));
        const { createLogger } = await Promise.resolve().then(() => __importStar(require('../core/logger')));
        const logger = createLogger('LeaderboardQuickAction');
        const leaderboardManager = new LeaderboardManager(logger);
        const ENTRIES_PER_PAGE = 12;
        const leaderboardData = await leaderboardManager.getPaginatedLeaderboard(guildId, 1, ENTRIES_PER_PAGE);
        const stats = await leaderboardManager.getLeaderboardStats(guildId);
        if (leaderboardData.entries.length === 0) {
            const embed = await createServerEconomyEmbed(guildId, 'Leaderboard');
            embed.setDescription(`${EMOJIS.MISC.MAGNIFYING} No users found yet!\n\nBe the first to earn some coins and claim the top spot!`)
                .setColor(COLORS.INFO);
            await interaction.reply({
                embeds: [embed],
                ephemeral: true
            });
            return;
        }
        const leaderboardEntries = [];
        let activeMembers = 0;
        let departedMembers = 0;
        for (const user of leaderboardData.entries) {
            try {
                let guildMember = interaction.guild?.members.cache.get(user.discordId);
                let isActive = true;
                let displayName = 'Unknown User';
                if (!guildMember) {
                    try {
                        guildMember = await interaction.guild?.members.fetch(user.discordId);
                    }
                    catch (fetchError) {
                        isActive = false;
                    }
                }
                if (guildMember && isActive) {
                    displayName = guildMember.displayName || guildMember.user.username;
                    activeMembers++;
                }
                else {
                    try {
                        const discordUser = await interaction.client.users.fetch(user.discordId);
                        displayName = `${discordUser.username} (Left Server)`;
                    }
                    catch {
                        displayName = 'Unknown User (Left Server)';
                    }
                    departedMembers++;
                }
                let positionEmoji = '';
                if (user.rank === 1)
                    positionEmoji = '🥇';
                else if (user.rank === 2)
                    positionEmoji = '🥈';
                else if (user.rank === 3)
                    positionEmoji = '🥉';
                else
                    positionEmoji = `${EMOJIS.ROLES.MEDAL}`;
                const statusIndicator = isActive ? '' : ' 👻';
                const formattedBalance = await formatServerCoins(guildId, user.balance);
                leaderboardEntries.push(`${positionEmoji} **#${user.rank}** ${displayName}${statusIndicator} — ${formattedBalance}`);
            }
            catch (error) {
                logger.error(`Error processing user ${user.discordId}:`, error);
                continue;
            }
        }
        if (leaderboardEntries.length === 0) {
            const embed = await createServerEconomyEmbed(guildId, 'Leaderboard');
            embed.setDescription(`${EMOJIS.MISC.MAGNIFYING} No users found!\n\nBe the first to earn some coins and claim the top spot!`)
                .setColor(COLORS.INFO);
            await interaction.reply({
                embeds: [embed],
                ephemeral: true
            });
            return;
        }
        let statusInfo = '';
        if (departedMembers > 0) {
            statusInfo = `\n\n👻 *${departedMembers} departed member${departedMembers > 1 ? 's' : ''} shown with ghost indicator*`;
        }
        const embed = await createServerEconomyEmbed(guildId, 'Leaderboard');
        embed.setDescription(`${EMOJIS.SUCCESS.TROPHY} **Top Players (Page ${leaderboardData.currentPage} of ${leaderboardData.totalPages})**\n\n` +
            leaderboardEntries.join('\n') +
            `\n\n${EMOJIS.ECONOMY.SPARKLES} *Keep earning to climb higher!*` +
            statusInfo);
        const formattedTotalEconomy = await formatServerCoins(guildId, stats.totalEconomyValue);
        const formattedAverage = await formatServerCoins(guildId, stats.averageBalance);
        const formattedMedian = await formatServerCoins(guildId, stats.medianBalance);
        embed.addFields({
            name: `${EMOJIS.MISC.CHART} Server Statistics`,
            value: `**Total Players:** ${stats.totalUsers}\n**Total Economy:** ${formattedTotalEconomy}\n**Average Balance:** ${formattedAverage}`,
            inline: true
        }, {
            name: `${EMOJIS.MISC.CALCULATOR} Balance Info`,
            value: `**Median Balance:** ${formattedMedian}\n**Highest Balance:** ${await formatServerCoins(guildId, stats.maxBalance)}\n**Page:** ${leaderboardData.currentPage}/${leaderboardData.totalPages}`,
            inline: true
        });
        embed.addFields({
            name: `${EMOJIS.MISC.CALENDAR} Last Updated`,
            value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
            inline: true
        });
        embed.setFooter({
            text: 'Use /balance to check your current position!'
        });
        const components = [];
        if (leaderboardData.totalPages > 1) {
            const navButtons = createNavigationButtons(leaderboardData.currentPage, leaderboardData.totalPages);
            navButtons.components.forEach(button => {
                const currentId = button.data.custom_id;
                if (currentId?.startsWith('nav_')) {
                    const action = currentId.replace('nav_', '');
                    button.setCustomId(`leaderboard_${action}_${guildId}`);
                }
            });
            components.push(navButtons);
        }
        await interaction.reply({
            embeds: [embed],
            components: components,
            ephemeral: false
        });
    }
    async handleRolesQuickAction(interaction, guildId) {
        const { ensureUser } = await Promise.resolve().then(() => __importStar(require('../services/economyService')));
        const { RoleForSale } = await Promise.resolve().then(() => __importStar(require('../models/User')));
        const { createServerEconomyEmbed, formatServerCoins, createQuickActionButtons, EMOJIS } = await Promise.resolve().then(() => __importStar(require('../utils/embedBuilder')));
        const discordId = interaction.user.id;
        const user = await ensureUser(discordId, guildId);
        const currentBalance = user.balance;
        const roles = await RoleForSale.find({ guildId }).sort({ price: 1 });
        const guildMember = await interaction.guild?.members.fetch(discordId);
        const userAchievements = [];
        if (guildMember) {
            for (const role of roles) {
                if (guildMember.roles.cache.has(role.roleId)) {
                    userAchievements.push({
                        roleId: role.roleId,
                        roleName: role.name,
                        price: role.price,
                        description: role.description
                    });
                }
            }
        }
        const embed = await createServerEconomyEmbed(guildId, 'Role Achievements');
        embed.setDescription(`${EMOJIS.ROLES.SHIELD} **Role Achievement Progress**\n\nRole achievements are automatically unlocked when you reach the required balance! Your balance represents your lifetime achievements and is never spent.`);
        const formattedBalance = await formatServerCoins(guildId, currentBalance);
        embed.addFields({
            name: `${EMOJIS.ECONOMY.MONEY} Your Current Balance`,
            value: formattedBalance,
            inline: true
        });
        const sortedRoles = roles;
        const roleEntries = [];
        for (const role of sortedRoles) {
            const isUnlocked = userAchievements.some(achievement => achievement.roleId === role.roleId);
            const canUnlock = currentBalance >= role.price;
            let statusEmoji = '';
            let statusText = '';
            if (isUnlocked) {
                statusEmoji = `${EMOJIS.SUCCESS.CHECK}`;
                statusText = '**UNLOCKED**';
            }
            else if (canUnlock) {
                statusEmoji = `${EMOJIS.ECONOMY.SPARKLES}`;
                statusText = '**AVAILABLE**';
            }
            else {
                statusEmoji = `${EMOJIS.MISC.LOCK}`;
                const needed = role.price - currentBalance;
                const formattedNeeded = await formatServerCoins(guildId, needed);
                statusText = `Need ${formattedNeeded} more`;
            }
            const formattedRequired = await formatServerCoins(guildId, role.price);
            roleEntries.push(`${statusEmoji} <@&${role.roleId}> — ${formattedRequired}\n   ${statusText}`);
        }
        if (roleEntries.length > 0) {
            const maxLength = 1024;
            let currentChunk = '';
            let chunkIndex = 1;
            for (const entry of roleEntries) {
                if (currentChunk.length + entry.length + 1 > maxLength) {
                    embed.addFields({
                        name: chunkIndex === 1 ? `${EMOJIS.ROLES.MEDAL} Available Role Achievements` : `${EMOJIS.ROLES.MEDAL} Available Role Achievements (continued)`,
                        value: currentChunk,
                        inline: false
                    });
                    currentChunk = entry;
                    chunkIndex++;
                }
                else {
                    if (currentChunk)
                        currentChunk += '\n';
                    currentChunk += entry;
                }
            }
            if (currentChunk) {
                embed.addFields({
                    name: chunkIndex === 1 ? `${EMOJIS.ROLES.MEDAL} Available Role Achievements` : `${EMOJIS.ROLES.MEDAL} Available Role Achievements (continued)`,
                    value: currentChunk,
                    inline: false
                });
            }
        }
        else {
            embed.addFields({
                name: `${EMOJIS.ROLES.MEDAL} Available Role Achievements`,
                value: 'No role achievements configured for this server.',
                inline: false
            });
        }
        const totalRoles = roles.length;
        const unlockedCount = userAchievements.length;
        const progressPercentage = Math.round((unlockedCount / totalRoles) * 100);
        const summaryBalance = await formatServerCoins(guildId, currentBalance);
        embed.addFields({
            name: `${EMOJIS.MISC.SCROLL} Achievement Progress`,
            value: `**${unlockedCount}/${totalRoles}** achievements unlocked (${progressPercentage}%)\n` +
                `${EMOJIS.ECONOMY.COINS} Your Balance: **${summaryBalance}**`,
            inline: false
        });
        embed.setFooter({
            text: 'Role achievements are automatically unlocked when you reach the required balance!'
        });
        const buttons = createQuickActionButtons();
        await interaction.reply({
            embeds: [embed],
            components: [buttons],
            ephemeral: false
        });
    }
    async handleRoleAchievementInfo(interaction, customId) {
        const roleId = customId.replace('buy_role_', '');
        await interaction.reply({
            content: `Role achievements are automatically unlocked when you reach the required PLC balance! Keep earning coins to unlock this achievement.`,
            ephemeral: true
        });
    }
    async handleAnnouncementConfirm(interaction) {
        const pendingAnnouncements = global.pendingAnnouncements;
        if (!pendingAnnouncements) {
            await interaction.reply({
                content: 'No pending announcements found. Please try the command again.',
                ephemeral: true
            });
            return;
        }
        const originalInteractionId = interaction.message?.interaction?.id;
        const announcementData = pendingAnnouncements.get(originalInteractionId);
        if (!announcementData) {
            await interaction.reply({
                content: 'Announcement data not found or expired. Please try the command again.',
                ephemeral: true
            });
            return;
        }
        pendingAnnouncements.delete(originalInteractionId);
        await interaction.deferUpdate();
        const announceModule = require('../commands/announce');
        await announceModule.processAnnouncement(interaction, announcementData);
    }
    async handleAnnouncementCancel(interaction) {
        const pendingAnnouncements = global.pendingAnnouncements;
        const originalInteractionId = interaction.message?.interaction?.id;
        if (pendingAnnouncements && originalInteractionId) {
            pendingAnnouncements.delete(originalInteractionId);
        }
        await interaction.update({
            content: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Announcement cancelled.`,
            embeds: [],
            components: []
        });
    }
    async handleTradeButton(interaction, customId) {
        try {
            const tradeService = this.app.getService('TradeService');
            const parts = customId.split('_');
            if (parts.length < 3) {
                throw new errorHandler_1.ValidationError('Invalid trade button format');
            }
            const action = parts[1];
            const tradeId = parts.slice(2).join('_');
            const trade = await tradeService.getTrade(tradeId);
            if (!trade) {
                throw new errorHandler_1.ValidationError('Trade not found');
            }
            if (!trade.involvesUser(interaction.user.id)) {
                throw new errorHandler_1.ValidationError('You are not a party to this trade');
            }
            switch (action) {
                case 'accept':
                    await this.handleTradeAccept(interaction, trade, tradeService);
                    break;
                case 'decline':
                    await this.handleTradeDecline(interaction, trade, tradeService);
                    break;
                case 'confirm':
                    await this.handleTradeConfirm(interaction, trade, tradeService);
                    break;
                case 'dispute':
                    await this.handleTradeDispute(interaction, trade, tradeService);
                    break;
                case 'cancel':
                    await this.handleTradeCancel(interaction, trade, tradeService);
                    break;
                case 'details':
                    await this.handleTradeDetails(interaction, trade);
                    break;
                default:
                    throw new errorHandler_1.ValidationError(`Unknown trade action: ${action}`);
            }
        }
        catch (error) {
            this.logger.error('Error handling trade button', { error, customId, userId: interaction.user.id });
            const embed = (0, embedBuilder_1.createErrorEmbed)('Trade Action Failed', error instanceof errorHandler_1.ValidationError ? error.message : 'An unexpected error occurred');
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [embed], ephemeral: true });
            }
            else {
                await interaction.reply({ embeds: [embed], ephemeral: true });
            }
        }
    }
    async handleTradeAccept(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        const updatedTrade = await tradeService.acceptTrade(trade.tradeId, interaction.user.id, interaction.client);
        await interaction.editReply({
            content: `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Trade Accepted!**\n\nThe trade has been activated and funds have been locked in escrow. You will receive further instructions via DM.`
        });
    }
    async handleTradeDecline(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        await tradeService.cancelTrade(trade.tradeId, interaction.user.id, 'Trade declined', interaction.client);
        await interaction.editReply({
            content: `${embedBuilder_1.EMOJIS.TRADE.CANCELLED} **Trade Declined**\n\nThe trade proposal has been declined and cancelled.`
        });
    }
    async handleTradeConfirm(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        const result = await tradeService.confirmTrade(trade.tradeId, interaction.user.id, interaction.client);
        if (result.completed) {
            await interaction.editReply({
                content: `${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Trade Completed!**\n\nBoth parties have confirmed completion. Funds have been released to the seller.`
            });
        }
        else {
            await interaction.editReply({
                content: `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Confirmation Recorded**\n\nYour confirmation has been recorded. Waiting for the other party to confirm.`
            });
        }
    }
    async handleTradeDispute(interaction, trade, tradeService) {
        await interaction.reply({
            content: `${embedBuilder_1.EMOJIS.TRADE.DISPUTED} **Dispute System**\n\nThe dispute system will be implemented in the next phase. For now, please contact an administrator for assistance.`,
            ephemeral: true
        });
    }
    async handleTradeCancel(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        await tradeService.cancelTrade(trade.tradeId, interaction.user.id, 'Cancelled by user', interaction.client);
        await interaction.editReply({
            content: `${embedBuilder_1.EMOJIS.TRADE.CANCELLED} **Trade Cancelled**\n\nThe trade has been cancelled and any escrowed funds have been refunded.`
        });
    }
    async handleTradeDetails(interaction, trade) {
        await interaction.reply({
            content: `${embedBuilder_1.EMOJIS.MISC.MAGNIFYING} **Trade Details**\n\nDetailed trade view will be implemented in the next phase.`,
            ephemeral: true
        });
    }
    async handleSuggestionButton(interaction, customId) {
        try {
            const parts = customId.split('_');
            const action = parts[1];
            const suggestionId = parts[2];
            const authorId = parts[3];
            switch (action) {
                case 'upvote':
                    await this.handleSuggestionVote(interaction, suggestionId, 'upvote');
                    break;
                case 'downvote':
                    await this.handleSuggestionVote(interaction, suggestionId, 'downvote');
                    break;
                case 'edit':
                    await this.handleSuggestionEdit(interaction, suggestionId, authorId);
                    break;
                case 'delete':
                    await this.handleSuggestionDelete(interaction, suggestionId, authorId);
                    break;
                default:
                    await interaction.reply({
                        content: 'Unknown suggestion action.',
                        ephemeral: true
                    });
            }
        }
        catch (error) {
            this.logger.error('[InteractionCreate] Error handling suggestion button', {
                error,
                customId,
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
            });
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: 'An error occurred while processing your request.',
                    ephemeral: true
                });
            }
        }
    }
    async handleSuggestionVote(interaction, suggestionId, voteType) {
        try {
            const result = await this.suggestionService.voteOnSuggestion(suggestionId, interaction.user.id, voteType, this.app.client);
            if (result.success) {
                const emoji = voteType === 'upvote' ? '🔺' : '🔻';
                const action = result.previousVote === voteType ? 'removed' :
                    result.previousVote ? 'changed to' : 'added';
                await interaction.reply({
                    content: `${emoji} ${action === 'removed' ? 'Vote removed!' : `${action} ${voteType}!`}\n` +
                        `**Upvotes:** ${result.upvoteCount} | **Downvotes:** ${result.downvoteCount}`,
                    ephemeral: true
                });
            }
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                await interaction.reply({
                    content: `❌ ${error.message}`,
                    ephemeral: true
                });
            }
            else {
                throw error;
            }
        }
    }
    async handleSuggestionEdit(interaction, suggestionId, authorId) {
        if (interaction.user.id !== authorId) {
            await interaction.reply({
                content: '❌ You can only edit your own suggestions.',
                ephemeral: true
            });
            return;
        }
        const suggestion = await this.suggestionService.getSuggestion(suggestionId);
        if (!suggestion) {
            await interaction.reply({
                content: '❌ Suggestion not found.',
                ephemeral: true
            });
            return;
        }
        const { ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');
        const modal = new ModalBuilder()
            .setCustomId(`suggestion_edit_modal_${suggestionId}`)
            .setTitle('Edit Suggestion');
        const textInput = new TextInputBuilder()
            .setCustomId('suggestion_content')
            .setLabel('Suggestion Content')
            .setStyle(TextInputStyle.Paragraph)
            .setValue(suggestion.content)
            .setMaxLength(2000)
            .setMinLength(10)
            .setRequired(true);
        const actionRow = new ActionRowBuilder().addComponents(textInput);
        modal.addComponents(actionRow);
        await interaction.showModal(modal);
    }
    async handleSuggestionDelete(interaction, suggestionId, authorId) {
        try {
            const canDelete = await this.suggestionService.canUserDeleteSuggestion(suggestionId, interaction.user.id, interaction.member);
            if (!canDelete) {
                await interaction.reply({
                    content: '❌ You can only delete your own suggestions or you need Administrator permission.',
                    ephemeral: true
                });
                return;
            }
            const success = await this.suggestionService.deleteSuggestion(suggestionId, this.app.client);
            if (success) {
                const isAuthor = interaction.user.id === authorId;
                const message = isAuthor
                    ? '🗑️ Your suggestion has been deleted successfully.'
                    : '🗑️ Suggestion deleted successfully by administrator.';
                await interaction.reply({
                    content: message,
                    ephemeral: true
                });
                if (!isAuthor) {
                    this.logger.info('[InteractionCreate] Admin deleted suggestion', {
                        suggestionId,
                        adminId: interaction.user.id,
                        originalAuthorId: authorId,
                        guildId: interaction.guild?.id
                    });
                }
            }
            else {
                await interaction.reply({
                    content: '❌ Failed to delete suggestion.',
                    ephemeral: true
                });
            }
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                await interaction.reply({
                    content: `❌ ${error.message}`,
                    ephemeral: true
                });
            }
            else {
                throw error;
            }
        }
    }
    async handleSuggestionEditModal(interaction, customId) {
        try {
            const suggestionId = customId.replace('suggestion_edit_modal_', '');
            const newContent = interaction.fields.getTextInputValue('suggestion_content');
            if (!newContent || newContent.trim().length < 10) {
                await interaction.reply({
                    content: '❌ Suggestions must be at least 10 characters long.',
                    ephemeral: true
                });
                return;
            }
            if (newContent.length > 2000) {
                await interaction.reply({
                    content: '❌ Suggestions cannot exceed 2000 characters.',
                    ephemeral: true
                });
                return;
            }
            const updatedSuggestion = await this.suggestionService.editSuggestion(suggestionId, newContent.trim(), this.app.client);
            if (updatedSuggestion) {
                await interaction.reply({
                    content: '✏️ Suggestion updated successfully!',
                    ephemeral: true
                });
            }
            else {
                await interaction.reply({
                    content: '❌ Failed to update suggestion.',
                    ephemeral: true
                });
            }
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                await interaction.reply({
                    content: `❌ ${error.message}`,
                    ephemeral: true
                });
            }
            else {
                throw error;
            }
        }
    }
}
exports.InteractionCreateEventHandler = InteractionCreateEventHandler;
