/**
 * Bankers Command
 * Set the banker role for the server - users with this role can use /give and /fine commands
 */

import { SlashCommandBuilder, PermissionFlagsBits } from 'discord.js';
import { BaseCommand, CommandCategory } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { createServerSuccessEmbed, createServerErrorEmbed, addUserInfo, EMOJIS } from '../../utils/embedBuilder';
import { ValidationError } from '../../utils/errorHandler';
import { setBankerRole, getBankerRoleId, validateBankerRole } from '../../utils/permissions/BankerPermissions';

/**
 * Bankers command implementation
 */
export class BankersCommand extends BaseCommand {
  constructor() {
    super({
      name: 'bankers',
      description: 'Set the banker role for this server (admin only)',
      category: CommandCategory.ADMIN,
      adminOnly: true,
      requiredFeatures: ['ECONOMY_SYSTEM'],
      requiredPermissions: ['Administrator'],
    });
  }

  /**
   * Customize the command builder
   */
  protected customizeCommand(command: SlashCommandBuilder): void {
    command
      .addSubcommand(subcommand =>
        subcommand
          .setName('set')
          .setDescription('Set the banker role for this server')
          .addRoleOption(option =>
            option.setName('role')
              .setDescription('The role that should have banker permissions')
              .setRequired(true)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('remove')
          .setDescription('Remove the banker role (only admins will have banker permissions)'))
      .addSubcommand(subcommand =>
        subcommand
          .setName('view')
          .setDescription('View the current banker role for this server'));
  }

  /**
   * Execute the bankers command
   */
  protected async executeCommand(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const subcommand = interaction.options.getSubcommand();
    const guildId = interaction.guild!.id;

    switch (subcommand) {
      case 'set':
        await this.handleSetBankerRole(context);
        break;
      case 'remove':
        await this.handleRemoveBankerRole(context);
        break;
      case 'view':
        await this.handleViewBankerRole(context);
        break;
      default:
        throw new ValidationError('Invalid subcommand');
    }
  }

  /**
   * Handle setting the banker role
   */
  private async handleSetBankerRole(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const role = interaction.options.getRole('role', true);
    const guildId = interaction.guild!.id;
    const member = interaction.member as any;

    try {
      // Validate the role
      await validateBankerRole(member, role.id);

      // Set the banker role
      await setBankerRole(guildId, role.id);

      // Create success embed
      const embed = await createServerSuccessEmbed(guildId, 'Banker Role Set Successfully!');
      embed.setDescription(
          `${EMOJIS.ADMIN.KEY} **Banker Role Configuration Updated**\n\n` +
          `The role **${role.name}** has been designated as the banker role for this server.\n\n` +
          `${EMOJIS.ECONOMY.COINS} **Permissions Granted:**\n` +
          `• Users with this role can now use the \`/give\` command\n` +
          `• Users with this role can now use the \`/fine\` command\n` +
          `• Administrators retain all banker permissions`
        )
        .addFields(
          {
            name: `${EMOJIS.ADMIN.HAMMER} Administrator`,
            value: `**${interaction.user.displayName}**`,
            inline: true
          },
          {
            name: `${EMOJIS.MISC.ROLE} Banker Role`,
            value: `**${role.name}**`,
            inline: true
          },
          {
            name: `${EMOJIS.MISC.USERS} Role Members`,
            value: `${role.members.size} member(s)`,
            inline: true
          }
        );

      // Add admin info
      addUserInfo(embed, interaction.user);

      await interaction.reply({
        embeds: [embed],
        ephemeral: false
      });

      this.logger.info(`Admin ${interaction.user.username} set banker role to ${role.name}`, {
        adminId: interaction.user.id,
        roleId: role.id,
        roleName: role.name,
        guildId: interaction.guild?.id,
      });

    } catch (error) {
      this.logger.error('Error setting banker role', { 
        error, 
        adminId: interaction.user.id, 
        roleId: role.id,
        roleName: role.name 
      });
      throw error;
    }
  }

  /**
   * Handle removing the banker role
   */
  private async handleRemoveBankerRole(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const guildId = interaction.guild!.id;

    try {
      // Get current banker role for display
      const currentRoleId = await getBankerRoleId(guildId);
      let currentRoleName = 'None';
      
      if (currentRoleId) {
        const currentRole = await interaction.guild!.roles.fetch(currentRoleId).catch(() => null);
        currentRoleName = currentRole?.name || 'Unknown Role';
      }

      // Remove the banker role
      await setBankerRole(guildId, null);

      // Create success embed
      const embed = await createServerSuccessEmbed(guildId, 'Banker Role Removed Successfully!');
      embed.setDescription(
          `${EMOJIS.ADMIN.WARNING} **Banker Role Configuration Cleared**\n\n` +
          `The banker role has been removed from this server.\n\n` +
          `${EMOJIS.ADMIN.SCALES} **Current Permissions:**\n` +
          `• Only administrators can use the \`/give\` command\n` +
          `• Only administrators can use the \`/fine\` command`
        )
        .addFields(
          {
            name: `${EMOJIS.ADMIN.HAMMER} Administrator`,
            value: `**${interaction.user.displayName}**`,
            inline: true
          },
          {
            name: `${EMOJIS.MISC.ROLE} Previous Banker Role`,
            value: `**${currentRoleName}**`,
            inline: true
          }
        );

      // Add admin info
      addUserInfo(embed, interaction.user);

      await interaction.reply({
        embeds: [embed],
        ephemeral: false
      });

      this.logger.info(`Admin ${interaction.user.username} removed banker role`, {
        adminId: interaction.user.id,
        previousRoleId: currentRoleId,
        previousRoleName: currentRoleName,
        guildId: interaction.guild?.id,
      });

    } catch (error) {
      this.logger.error('Error removing banker role', { 
        error, 
        adminId: interaction.user.id
      });
      throw error;
    }
  }

  /**
   * Handle viewing the current banker role
   */
  private async handleViewBankerRole(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const guildId = interaction.guild!.id;

    try {
      // Get current banker role
      const currentRoleId = await getBankerRoleId(guildId);
      
      let embed;
      
      if (currentRoleId) {
        const currentRole = await interaction.guild!.roles.fetch(currentRoleId).catch(() => null);
        
        if (currentRole) {
          embed = await createServerSuccessEmbed(guildId, 'Current Banker Role');
          embed.setDescription(
              `${EMOJIS.MISC.ROLE} **Current Banker Role Configuration**\n\n` +
              `The role **${currentRole.name}** is currently designated as the banker role.\n\n` +
              `${EMOJIS.ECONOMY.COINS} **Permissions:**\n` +
              `• Members with this role can use \`/give\` and \`/fine\` commands\n` +
              `• Administrators also retain all banker permissions`
            )
            .addFields(
              {
                name: `${EMOJIS.MISC.ROLE} Banker Role`,
                value: `**${currentRole.name}**`,
                inline: true
              },
              {
                name: `${EMOJIS.MISC.USERS} Role Members`,
                value: `${currentRole.members.size} member(s)`,
                inline: true
              },
              {
                name: `${EMOJIS.MISC.ID} Role ID`,
                value: `\`${currentRole.id}\``,
                inline: true
              }
            );
        } else {
          // Role was deleted but still in config
          embed = await createServerErrorEmbed(guildId, 'Banker Role Configuration Issue');
          embed.setDescription(
              `${EMOJIS.ADMIN.WARNING} **Configuration Issue Detected**\n\n` +
              `A banker role is configured but the role no longer exists.\n` +
              `Please use \`/bankers remove\` to clear the configuration or \`/bankers set\` to set a new role.`
            );
        }
      } else {
        embed = await createServerSuccessEmbed(guildId, 'No Banker Role Set');
        embed.setDescription(
            `${EMOJIS.ADMIN.SCALES} **No Banker Role Configured**\n\n` +
            `Currently, only administrators can use \`/give\` and \`/fine\` commands.\n\n` +
            `Use \`/bankers set @role\` to designate a banker role.`
          );
      }

      // Add admin info
      addUserInfo(embed, interaction.user);

      await interaction.reply({
        embeds: [embed],
        ephemeral: true
      });

    } catch (error) {
      this.logger.error('Error viewing banker role', { 
        error, 
        adminId: interaction.user.id
      });
      throw error;
    }
  }
}
