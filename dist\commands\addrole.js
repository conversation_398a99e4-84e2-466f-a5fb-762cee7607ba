"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const errorHandler_1 = require("../utils/errorHandler");
const roleResolver_1 = require("../utils/roleResolver");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('addrole')
        .setDescription('Add a role achievement (admin only)')
        .addRoleOption(option => option.setName('role').setDescription('Role to add as achievement').setRequired(true))
        .addIntegerOption(option => option.setName('price').setDescription('Required PLC balance to unlock').setRequired(true))
        .addStringOption(option => option.setName('description').setDescription('Achievement description').setRequired(false))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError();
        }
        const role = interaction.options.getRole('role', true);
        const price = interaction.options.getInteger('price', true);
        const description = interaction.options.getString('description') || '';
        if (price <= 0) {
            throw new errorHandler_1.ValidationError('Price must be greater than zero.');
        }
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        try {
            const fullRole = interaction.guild.roles.cache.get(role.id) || await interaction.guild.roles.fetch(role.id);
            if (!fullRole) {
                throw new errorHandler_1.ValidationError('Role not found in guild');
            }
            await (0, roleResolver_1.validateRolePermissions)(interaction.guild, fullRole);
            const exists = await User_1.RoleForSale.findOne({ roleId: fullRole.id, guildId: interaction.guild.id });
            if (exists) {
                throw new errorHandler_1.ValidationError(`Role achievement "${fullRole.name}" already exists.`);
            }
            await User_1.RoleForSale.create({
                guildId: interaction.guild.id,
                roleId: fullRole.id,
                name: fullRole.name,
                price,
                description
            });
            const coinName = await configurableConstants_1.default.getCoinName(interaction.guild.id);
            const successMessage = `Role achievement **${fullRole.name}** added with requirement of **${price}** ${coinName}! 🏆`;
            await interaction.reply({
                content: successMessage,
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(error.message);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to add role achievement.');
            }
        }
    })
};
