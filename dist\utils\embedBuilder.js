"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EMOJIS = exports.COLORS = void 0;
exports.createBaseEmbed = createBaseEmbed;
exports.createServerBaseEmbed = createServerBaseEmbed;
exports.createSuccessEmbed = createSuccessEmbed;
exports.createErrorEmbed = createErrorEmbed;
exports.createEconomyEmbed = createEconomyEmbed;
exports.createAdminEmbed = createAdminEmbed;
exports.createServerSuccessEmbed = createServerSuccessEmbed;
exports.createServerErrorEmbed = createServerErrorEmbed;
exports.createServerEconomyEmbed = createServerEconomyEmbed;
exports.createServerAdminEmbed = createServerAdminEmbed;
exports.createWarningEmbed = createWarningEmbed;
exports.addUserInfo = addUserInfo;
exports.createQuickActionButtons = createQuickActionButtons;
exports.createSalaryActionButtons = createSalaryActionButtons;
exports.createNavigationButtons = createNavigationButtons;
exports.createConfirmationButtons = createConfirmationButtons;
exports.formatCoins = formatCoins;
exports.formatServerCoins = formatServerCoins;
exports.formatServerCoinsWithSymbol = formatServerCoinsWithSymbol;
exports.createLoadingEmbed = createLoadingEmbed;
const discord_js_1 = require("discord.js");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
exports.COLORS = {
    PRIMARY: '#dd7d00',
    SUCCESS: '#00ff00',
    ERROR: '#ff0000',
    WARNING: '#ffaa00',
    INFO: '#0099ff',
    GOLD: '#ffd700'
};
exports.EMOJIS = {
    ECONOMY: {
        COINS: '🪙',
        MONEY: '💰',
        DIAMOND: '💎',
        DOLLAR: '💲',
        SPARKLES: '✨',
        BANK: '🏦',
        CHART: '📈',
        TRANSFER: '🔄'
    },
    SUCCESS: {
        CHECK: '✅',
        PARTY: '🎉',
        STAR: '⭐',
        CROWN: '👑',
        TROPHY: '🏆',
        THUMBS_UP: '👍',
        REFRESH: '🔄'
    },
    ROLES: {
        MEDAL: '🏅',
        MASK: '🎭',
        SHIELD: '🔰',
        ARMOR: '🛡️',
        BADGE: '📛',
        RIBBON: '🎀'
    },
    ACTIONS: {
        LIGHTNING: '⚡',
        ROCKET: '🚀',
        TARGET: '🎯',
        FIRE: '🔥',
        MAGIC: '🪄',
        GEAR: '⚙️',
        SENDER: '👤'
    },
    ADMIN: {
        SCALES: '⚖️',
        HAMMER: '🔨',
        WARNING: '⚠️',
        TOOLS: '🛠️',
        LOCK: '🔒',
        KEY: '🔑',
        INFO: 'ℹ️',
        LIST: '📋',
        CLOCK: '🕐',
        SETTINGS: '⚙️'
    },
    MILESTONE: {
        TROPHY: '🏆',
        STAR: '⭐',
        MEDAL: '🏅',
        STREAK: '🔥',
        DIVERSITY: '🌈',
        VOICE: '🎤',
        MESSAGE: '💬',
        ANNIVERSARY: '🎂',
        PROGRESS: '📊',
        ACHIEVEMENT: '🎯'
    },
    TRADE: {
        HANDSHAKE: '🤝',
        PACKAGE: '📦',
        SCALES: '⚖️',
        HOURGLASS: '⏳',
        TIMER: '⏰',
        PENDING: '🟡',
        ACTIVE: '🟢',
        COMPLETED: '✅',
        CANCELLED: '❌',
        EXPIRED: '⏰',
        DISPUTED: '⚠️',
        ESCROW: '🔒',
        RELEASE: '🔓',
        PROPOSAL: '📋',
        CONFIRMATION: '✔️'
    },
    MISC: {
        CLOCK: '🕐',
        CALENDAR: '📅',
        BOOK: '📖',
        SCROLL: '📜',
        MAGNIFYING: '🔍',
        BELL: '🔔',
        LIGHTBULB: '💡',
        ID: '🆔',
        USER: '👤',
        GUILD: '🏰',
        ROLE: '🎭',
        SPARKLES: '✨',
        LINK: '🔗',
        TAG: '🏷️',
        ATTACHMENT: '📎',
        CHART: '📊',
        CALCULATOR: '🧮',
        LOCK: '🔒'
    },
    ERROR: {
        GENERAL: '❌',
        WARNING: '⚠️',
        STOP: '🛑'
    }
};
function createBaseEmbed(title, description, color = exports.COLORS.PRIMARY) {
    const embed = new discord_js_1.EmbedBuilder()
        .setColor(color)
        .setTimestamp();
    if (title)
        embed.setTitle(title);
    if (description)
        embed.setDescription(description);
    return embed;
}
async function createServerBaseEmbed(guildId, title, description, color) {
    const serverColor = color || await configurableConstants_1.default.getEmbedColor(guildId);
    const embed = new discord_js_1.EmbedBuilder()
        .setColor(serverColor)
        .setTimestamp();
    if (title)
        embed.setTitle(title);
    if (description)
        embed.setDescription(description);
    return embed;
}
function createSuccessEmbed(title, description) {
    return createBaseEmbed(`${exports.EMOJIS.SUCCESS.CHECK} ${title}`, description, exports.COLORS.SUCCESS);
}
function createErrorEmbed(title, description) {
    return createBaseEmbed(`${exports.EMOJIS.ADMIN.WARNING} ${title}`, description, exports.COLORS.ERROR);
}
function createEconomyEmbed(title, description) {
    return createBaseEmbed(`${exports.EMOJIS.ECONOMY.COINS} ${title}`, description, exports.COLORS.PRIMARY);
}
function createAdminEmbed(title, description) {
    return createBaseEmbed(`${exports.EMOJIS.ADMIN.HAMMER} ${title}`, description, exports.COLORS.WARNING);
}
async function createServerSuccessEmbed(guildId, title, description) {
    const embed = await createServerBaseEmbed(guildId, `${exports.EMOJIS.SUCCESS.CHECK} ${title}`, description, exports.COLORS.SUCCESS);
    const footer = await configurableConstants_1.default.getEmbedFooter(guildId);
    return embed.setFooter({ text: footer });
}
async function createServerErrorEmbed(guildId, title, description) {
    const embed = await createServerBaseEmbed(guildId, `${exports.EMOJIS.ADMIN.WARNING} ${title}`, description, exports.COLORS.ERROR);
    const footer = await configurableConstants_1.default.getEmbedFooter(guildId);
    return embed.setFooter({ text: footer });
}
async function createServerEconomyEmbed(guildId, title, description) {
    const embed = await createServerBaseEmbed(guildId, `${exports.EMOJIS.ECONOMY.COINS} ${title}`, description);
    const footer = await configurableConstants_1.default.getEmbedFooter(guildId);
    return embed.setFooter({ text: footer });
}
async function createServerAdminEmbed(guildId, title, description) {
    const embed = await createServerBaseEmbed(guildId, `${exports.EMOJIS.ADMIN.HAMMER} ${title}`, description, exports.COLORS.WARNING);
    const footer = await configurableConstants_1.default.getEmbedFooter(guildId);
    return embed.setFooter({ text: footer });
}
function createWarningEmbed(title, description) {
    return createBaseEmbed(`${exports.EMOJIS.ADMIN.WARNING} ${title}`, description, exports.COLORS.WARNING);
}
function addUserInfo(embed, user) {
    return embed
        .setAuthor({
        name: user.displayName || user.username,
        iconURL: user.displayAvatarURL()
    })
        .setThumbnail(user.displayAvatarURL({ size: 128 }));
}
function createQuickActionButtons() {
    return new discord_js_1.ActionRowBuilder().addComponents(new discord_js_1.ButtonBuilder()
        .setCustomId('quick_balance')
        .setLabel('Balance')
        .setEmoji(exports.EMOJIS.ECONOMY.COINS)
        .setStyle(discord_js_1.ButtonStyle.Secondary), new discord_js_1.ButtonBuilder()
        .setCustomId('quick_leaderboard')
        .setLabel('Leaderboard')
        .setEmoji(exports.EMOJIS.SUCCESS.TROPHY)
        .setStyle(discord_js_1.ButtonStyle.Secondary), new discord_js_1.ButtonBuilder()
        .setCustomId('quick_roles')
        .setLabel('Available Roles')
        .setEmoji(exports.EMOJIS.ROLES.MEDAL)
        .setStyle(discord_js_1.ButtonStyle.Secondary));
}
function createSalaryActionButtons() {
    return new discord_js_1.ActionRowBuilder().addComponents(new discord_js_1.ButtonBuilder()
        .setCustomId('salary_balance')
        .setLabel('Check Balance')
        .setEmoji(exports.EMOJIS.ECONOMY.COINS)
        .setStyle(discord_js_1.ButtonStyle.Primary), new discord_js_1.ButtonBuilder()
        .setCustomId('salary_leaderboard')
        .setLabel('Leaderboard')
        .setEmoji(exports.EMOJIS.SUCCESS.TROPHY)
        .setStyle(discord_js_1.ButtonStyle.Secondary), new discord_js_1.ButtonBuilder()
        .setCustomId('salary_roles')
        .setLabel('Available Roles')
        .setEmoji(exports.EMOJIS.ROLES.MEDAL)
        .setStyle(discord_js_1.ButtonStyle.Secondary));
}
function createNavigationButtons(currentPage, totalPages, disabled = false) {
    return new discord_js_1.ActionRowBuilder().addComponents(new discord_js_1.ButtonBuilder()
        .setCustomId('nav_first')
        .setLabel('First')
        .setEmoji('⏮️')
        .setStyle(discord_js_1.ButtonStyle.Secondary)
        .setDisabled(disabled || currentPage === 1), new discord_js_1.ButtonBuilder()
        .setCustomId('nav_prev')
        .setLabel('Previous')
        .setEmoji('◀️')
        .setStyle(discord_js_1.ButtonStyle.Secondary)
        .setDisabled(disabled || currentPage === 1), new discord_js_1.ButtonBuilder()
        .setCustomId('nav_next')
        .setLabel('Next')
        .setEmoji('▶️')
        .setStyle(discord_js_1.ButtonStyle.Secondary)
        .setDisabled(disabled || currentPage === totalPages), new discord_js_1.ButtonBuilder()
        .setCustomId('nav_last')
        .setLabel('Last')
        .setEmoji('⏭️')
        .setStyle(discord_js_1.ButtonStyle.Secondary)
        .setDisabled(disabled || currentPage === totalPages));
}
function createConfirmationButtons(confirmId, cancelId) {
    return new discord_js_1.ActionRowBuilder().addComponents(new discord_js_1.ButtonBuilder()
        .setCustomId(confirmId)
        .setLabel('Confirm')
        .setEmoji(exports.EMOJIS.SUCCESS.CHECK)
        .setStyle(discord_js_1.ButtonStyle.Success), new discord_js_1.ButtonBuilder()
        .setCustomId(cancelId)
        .setLabel('Cancel')
        .setEmoji('❌')
        .setStyle(discord_js_1.ButtonStyle.Danger));
}
function formatCoins(amount) {
    return `${exports.EMOJIS.ECONOMY.COINS} **${amount.toLocaleString()}** coins`;
}
async function formatServerCoins(guildId, amount) {
    return await configurableConstants_1.default.formatCoins(guildId, amount);
}
async function formatServerCoinsWithSymbol(guildId, amount) {
    return await configurableConstants_1.default.formatCoinsWithSymbol(guildId, amount);
}
function createLoadingEmbed(message = 'Processing...') {
    return createBaseEmbed(`${exports.EMOJIS.MISC.CLOCK} ${message}`, 'Please wait while we process your request.', exports.COLORS.INFO);
}
