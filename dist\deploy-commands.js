"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const dotenv_1 = __importDefault(require("dotenv"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config();
async function deployCommands() {
    const commands = [];
    try {
        console.log('🔄 Loading commands...');
        const commandsPath = path_1.default.join(__dirname, 'commands');
        const commandFiles = fs_1.default.readdirSync(commandsPath).filter(file => (file.endsWith('.js') || file.endsWith('.ts')) &&
            !file.includes('index') &&
            !file.includes('Manager') &&
            !file.includes('Base'));
        const skipFiles = new Set([
            'balance.ts', 'balance.js',
            'pay.ts', 'pay.js',
            'give.ts', 'give.js',
            'enhancerole.ts', 'enhancerole.js',
            'updatenames.ts', 'updatenames.js'
        ]);
        for (const file of commandFiles) {
            if (skipFiles.has(file)) {
                console.log(`⏭️  Skipping ${file} (handled by new architecture)`);
                continue;
            }
            try {
                const command = require(path_1.default.join(commandsPath, file));
                if (command.data) {
                    commands.push(command.data.toJSON());
                    console.log(`✅ Loaded legacy command: ${command.data.name}`);
                }
            }
            catch (error) {
                console.warn(`⚠️  Failed to load ${file}:`, error);
            }
        }
        try {
            const { commandManager } = require('./commands/CommandManager');
            await commandManager.loadCommands();
            const existingCommandNames = new Set(commands.map(cmd => cmd.name));
            const newCommands = commandManager.getDiscordCommands();
            for (const [name, command] of newCommands) {
                if (command.data) {
                    if (existingCommandNames.has(name)) {
                        console.log(`⏭️  Skipping new architecture command '${name}' - legacy version already loaded`);
                        continue;
                    }
                    commands.push(command.data.toJSON());
                    console.log(`✅ Loaded new command: ${name}`);
                }
            }
        }
        catch (error) {
            console.warn('⚠️  Failed to load new architecture commands:', error);
        }
        console.log(`📊 Total commands to deploy: ${commands.length}`);
        if (commands.length === 0) {
            console.error('❌ No commands found to deploy!');
            return;
        }
        const rest = new discord_js_1.REST({ version: '10' }).setToken(process.env.BOT_TOKEN);
        console.log('🚀 Started refreshing application (/) commands...');
        const data = await rest.put(discord_js_1.Routes.applicationCommands(process.env.CLIENT_ID), { body: commands });
        console.log(`✅ Successfully reloaded ${data.length} application (/) commands.`);
        console.log('\n📋 Deployed commands:');
        for (const cmd of data) {
            console.log(`   • /${cmd.name} - ${cmd.description}`);
        }
    }
    catch (error) {
        console.error('❌ Error deploying commands:', error);
        process.exit(1);
    }
}
deployCommands();
