"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkAndAssignRoles = checkAndAssignRoles;
exports.sendRoleAchievementNotifications = sendRoleAchievementNotifications;
exports.getUserAchievementRoles = getUserAchievementRoles;
const User_1 = require("../models/User");
const economyService_1 = require("./economyService");
const embedBuilder_1 = require("../utils/embedBuilder");
const errorHandler_1 = require("../utils/errorHandler");
async function checkAndAssignRoles(client, discordId, guildId, newBalance) {
    try {
        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
            console.warn(`Guild ${guildId} not found for role assignment`);
            return null;
        }
        let member;
        try {
            member = await guild.members.fetch(discordId);
        }
        catch (error) {
            console.warn(`Member ${discordId} not found in guild ${guildId}`);
            return null;
        }
        const availableRoles = await User_1.RoleForSale.find({ guildId }).sort({ price: 1 });
        if (!availableRoles.length) {
            return null;
        }
        const rolesToAssign = [];
        for (const roleForSale of availableRoles) {
            if (newBalance >= roleForSale.price) {
                if (!member.roles.cache.has(roleForSale.roleId)) {
                    const discordRole = guild.roles.cache.get(roleForSale.roleId);
                    if (discordRole) {
                        rolesToAssign.push({
                            roleId: roleForSale.roleId,
                            roleName: roleForSale.name,
                            price: roleForSale.price,
                            description: roleForSale.description,
                            discordRole: discordRole
                        });
                    }
                    else {
                        console.warn(`Role ${roleForSale.roleId} (${roleForSale.name}) not found in guild ${guildId}`);
                    }
                }
            }
        }
        if (rolesToAssign.length === 0) {
            return null;
        }
        const assignedRoles = [];
        for (const roleInfo of rolesToAssign) {
            try {
                await member.roles.add(roleInfo.discordRole);
                await (0, economyService_1.adjustBalance)(discordId, guildId, 0, 'role_achievement', `Achievement unlocked: ${roleInfo.roleName} (Required: ${roleInfo.price} PLC)`);
                assignedRoles.push({
                    roleId: roleInfo.roleId,
                    roleName: roleInfo.roleName,
                    price: roleInfo.price,
                    description: roleInfo.description
                });
                console.log(`Assigned role ${roleInfo.roleName} to user ${discordId} in guild ${guildId}`);
            }
            catch (error) {
                console.error(`Failed to assign role ${roleInfo.roleName} to user ${discordId}:`, error);
            }
        }
        if (assignedRoles.length > 0) {
            return {
                rolesAssigned: assignedRoles,
                member: member,
                newBalance: newBalance
            };
        }
        return null;
    }
    catch (error) {
        console.error('Error in checkAndAssignRoles:', error);
        throw new errorHandler_1.DatabaseError('Failed to check and assign roles');
    }
}
async function sendRoleAchievementNotifications(result, client) {
    try {
        const { rolesAssigned, member, newBalance } = result;
        if (rolesAssigned.length === 1) {
            const role = rolesAssigned[0];
            const guildId = member.guild.id;
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, '🎉 Achievement Unlocked!');
            embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Congratulations!**\n\n` +
                `You have automatically unlocked the **${role.roleName}** role achievement!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} Achievement Unlocked`,
                value: `**${role.roleName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Required Balance`,
                value: await (0, embedBuilder_1.formatServerCoins)(guildId, role.price),
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.MONEY} Your Balance`,
                value: await (0, embedBuilder_1.formatServerCoins)(guildId, newBalance),
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Achievement Details`,
                value: role.description || 'Congratulations on your achievement!',
                inline: false
            })
                .setFooter({
                text: 'Role achievements are automatically unlocked when you reach the required PLC balance!'
            });
            (0, embedBuilder_1.addUserInfo)(embed, member.user);
            try {
                await member.send({ embeds: [embed] });
            }
            catch (error) {
                console.warn(`Could not send DM to user ${member.id}:`, error);
            }
        }
        else {
            const guildId = member.guild.id;
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, '🎉 Multiple Achievements Unlocked!');
            embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Congratulations!**\n\n` +
                `You have automatically unlocked **${rolesAssigned.length}** role achievements!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ECONOMY.MONEY} Your Balance`,
                value: await (0, embedBuilder_1.formatServerCoins)(guildId, newBalance),
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} Achievements Unlocked`,
                value: (await Promise.all(rolesAssigned.map(async (role) => `• **${role.roleName}** (${await (0, embedBuilder_1.formatServerCoins)(guildId, role.price)})`))).join('\n'),
                inline: false
            })
                .setFooter({
                text: 'Role achievements are automatically unlocked when you reach the required PLC balance!'
            });
            (0, embedBuilder_1.addUserInfo)(embed, member.user);
            try {
                await member.send({ embeds: [embed] });
            }
            catch (error) {
                console.warn(`Could not send DM to user ${member.id}:`, error);
            }
        }
    }
    catch (error) {
        console.error('Error sending role achievement notifications:', error);
    }
}
async function getUserAchievementRoles(member) {
    try {
        const availableRoles = await User_1.RoleForSale.find({ guildId: member.guild.id });
        const userAchievements = [];
        for (const roleForSale of availableRoles) {
            if (member.roles.cache.has(roleForSale.roleId)) {
                userAchievements.push({
                    roleId: roleForSale.roleId,
                    roleName: roleForSale.name,
                    price: roleForSale.price,
                    description: roleForSale.description
                });
            }
        }
        return userAchievements.sort((a, b) => a.price - b.price);
    }
    catch (error) {
        console.error('Error getting user achievement roles:', error);
        return [];
    }
}
