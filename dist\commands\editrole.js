"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const errorHandler_1 = require("../utils/errorHandler");
const roleResolver_1 = require("../utils/roleResolver");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('editrole')
        .setDescription('Edit a role achievement (admin only)')
        .addStringOption(option => option.setName('role').setDescription('Role name or ID').setRequired(true))
        .addStringOption(option => option.setName('name').setDescription('New display name for the achievement').setRequired(false))
        .addIntegerOption(option => option.setName('price').setDescription('New required PLC balance to unlock').setRequired(false))
        .addStringOption(option => option.setName('description').setDescription('New achievement description').setRequired(false))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError();
        }
        const roleInput = interaction.options.getString('role', true);
        const newName = interaction.options.getString('name');
        const newPrice = interaction.options.getInteger('price');
        const newDescription = interaction.options.getString('description');
        if (newPrice !== null && newPrice <= 0) {
            throw new errorHandler_1.ValidationError('Price must be greater than zero.');
        }
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        try {
            const roleResolution = await (0, roleResolver_1.resolveRole)(interaction.guild, roleInput);
            const { role, resolvedBy, confidence } = roleResolution;
            await (0, roleResolver_1.validateRolePermissions)(interaction.guild, role);
            const update = {};
            if (newName)
                update.name = newName;
            if (newPrice !== null)
                update.price = newPrice;
            if (newDescription !== null)
                update.description = newDescription;
            if (Object.keys(update).length === 0) {
                throw new errorHandler_1.ValidationError('No changes specified. Please provide at least one field to update.');
            }
            const updated = await User_1.RoleForSale.findOneAndUpdate({ roleId: role.id, guildId: interaction.guild.id }, update, { new: true });
            if (!updated) {
                throw new errorHandler_1.ValidationError(`Role achievement "${role.name}" not found.`);
            }
            let successMessage = `Role achievement **${updated.name}** updated successfully! ✏️`;
            if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                successMessage += `\n*Note: Resolved "${roleInput}" to "${role.name}"*`;
            }
            await interaction.reply({
                content: successMessage,
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(error.message);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to update role achievement.');
            }
        }
    })
};
