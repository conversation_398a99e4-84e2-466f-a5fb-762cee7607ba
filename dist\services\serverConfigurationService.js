"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerConfigurationService = void 0;
const ServerConfiguration_1 = __importDefault(require("../models/ServerConfiguration"));
class ServerConfigurationService {
    static async getServerConfig(guildId) {
        const timestamp = new Date().toISOString();
        console.log(`[SERVER CONFIG DEBUG ${timestamp}] getServerConfig() called for guild: ${guildId}`);
        const cached = this.configCache.get(guildId);
        const expiry = this.cacheExpiry.get(guildId);
        if (cached && expiry && Date.now() < expiry) {
            console.log(`[SERVER CONFIG DEBUG ${timestamp}] Cache HIT for guild: ${guildId} - Nation: "${cached.nationName}", Coin: "${cached.coinName}"`);
            return cached;
        }
        console.log(`[SERVER CONFIG DEBUG ${timestamp}] Cache MISS for guild: ${guildId} - Fetching from database`);
        try {
            let config = await ServerConfiguration_1.default.findByGuildId(guildId);
            if (!config) {
                console.log(`[SERVER CONFIG DEBUG ${timestamp}] No config found for guild: ${guildId} - Creating default config`);
                config = await ServerConfiguration_1.default.create({
                    guildId,
                    nationName: 'Phalanx Order',
                    coinName: 'Phalanx Loyalty Coins',
                    coinSymbol: 'PLC',
                    embedColor: '#dd7d00'
                });
                console.log(`[SERVER CONFIG DEBUG ${timestamp}] Default config created for guild: ${guildId}`);
            }
            else {
                console.log(`[SERVER CONFIG DEBUG ${timestamp}] Config found for guild: ${guildId} - Nation: "${config.nationName}", Coin: "${config.coinName}", Symbol: "${config.coinSymbol}"`);
            }
            this.configCache.set(guildId, config);
            this.cacheExpiry.set(guildId, Date.now() + this.CACHE_TTL);
            console.log(`[SERVER CONFIG DEBUG ${timestamp}] Config cached for guild: ${guildId}`);
            return config;
        }
        catch (error) {
            console.error(`[SERVER CONFIG DEBUG ${timestamp}] Database error for guild: ${guildId}:`, error);
            throw error;
        }
    }
    static async updateServerConfig(guildId, updates) {
        if (updates.embedColor) {
            updates.embedColor = this.validateAndNormalizeColor(updates.embedColor);
        }
        const config = await ServerConfiguration_1.default.createOrUpdate(guildId, updates);
        this.configCache.set(guildId, config);
        this.cacheExpiry.set(guildId, Date.now() + this.CACHE_TTL);
        return config;
    }
    static async getNationName(guildId) {
        const config = await this.getServerConfig(guildId);
        return config.nationName;
    }
    static async getCoinName(guildId) {
        const config = await this.getServerConfig(guildId);
        return config.coinName;
    }
    static async getCoinSymbol(guildId) {
        const config = await this.getServerConfig(guildId);
        return config.coinSymbol;
    }
    static async getEmbedColor(guildId) {
        const config = await this.getServerConfig(guildId);
        return config.embedColor;
    }
    static async formatCoins(guildId, amount) {
        const config = await this.getServerConfig(guildId);
        const plural = amount === 1 ? config.coinName.replace(/s$/, '') : config.coinName;
        return `🪙 **${amount.toLocaleString()}** ${plural}`;
    }
    static async getEmbedFooter(guildId) {
        const config = await this.getServerConfig(guildId);
        return `${config.nationName} Economy System`;
    }
    static validateAndNormalizeColor(color) {
        const trimmedColor = color.trim();
        const lowerColor = trimmedColor.toLowerCase();
        const colorNames = [
            'red', 'green', 'blue', 'yellow', 'orange', 'purple', 'pink',
            'cyan', 'magenta', 'lime', 'indigo', 'violet', 'brown', 'black',
            'white', 'gray', 'grey', 'gold', 'silver', 'navy', 'teal', 'maroon',
            'aqua', 'fuchsia', 'olive', 'darkred', 'darkgreen', 'darkblue',
            'lightgray', 'lightgrey', 'darkgray', 'darkgrey', 'lightblue',
            'lightgreen', 'lightyellow', 'lightpink', 'lightcyan', 'darkviolet',
            'darkorange', 'darkgoldenrod', 'mediumblue', 'mediumseagreen',
            'mediumorchid', 'mediumturquoise', 'mediumvioletred', 'royalblue',
            'steelblue', 'slateblue', 'springgreen', 'forestgreen', 'seagreen',
            'crimson', 'coral', 'salmon', 'tomato', 'orangered', 'hotpink',
            'deeppink', 'palevioletred', 'plum', 'orchid', 'thistle', 'lavender'
        ];
        if (colorNames.includes(lowerColor)) {
            return lowerColor;
        }
        let hexColor = trimmedColor;
        if (hexColor.startsWith('#')) {
            hexColor = hexColor.substring(1);
        }
        if (/^[A-Fa-f0-9]{6}$/.test(hexColor)) {
            return `#${hexColor.toUpperCase()}`;
        }
        else if (/^[A-Fa-f0-9]{3}$/.test(hexColor)) {
            const expanded = hexColor.split('').map(char => char + char).join('');
            return `#${expanded.toUpperCase()}`;
        }
        throw new Error(`Invalid color format: "${color}". Supported formats:\n` +
            `• Hex codes: #FF0000, FF0000, #F00, F00\n` +
            `• Color names: red, blue, green, purple, etc. (case-insensitive)\n` +
            `• Examples: "#FF0000", "ff0000", "#F00", "RED", "Blue", "purple"`);
    }
    static clearCache(guildId) {
        if (guildId) {
            this.configCache.delete(guildId);
            this.cacheExpiry.delete(guildId);
        }
        else {
            this.configCache.clear();
            this.cacheExpiry.clear();
        }
    }
    static async initializeDefaults(guildIds) {
        const promises = guildIds.map(async (guildId) => {
            const existing = await ServerConfiguration_1.default.findByGuildId(guildId);
            if (!existing) {
                await ServerConfiguration_1.default.create({
                    guildId,
                    nationName: 'Phalanx Order',
                    coinName: 'Phalanx Loyalty Coins',
                    coinSymbol: 'PLC',
                    embedColor: '#dd7d00'
                });
            }
        });
        await Promise.all(promises);
    }
    static async getAllConfigurations() {
        return await ServerConfiguration_1.default.find({}).sort({ updatedAt: -1 });
    }
}
exports.ServerConfigurationService = ServerConfigurationService;
ServerConfigurationService.configCache = new Map();
ServerConfigurationService.cacheExpiry = new Map();
ServerConfigurationService.CACHE_TTL = 5 * 60 * 1000;
exports.default = ServerConfigurationService;
