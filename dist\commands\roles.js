"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const User_2 = __importDefault(require("../models/User"));
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const roleAssignmentService_1 = require("../services/roleAssignmentService");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('roles')
        .setDescription('Display all role achievements available to unlock'),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        try {
            const guildId = interaction.guild?.id;
            if (!guildId) {
                throw new Error('This command can only be used in a server');
            }
            const roles = await User_1.RoleForSale.find({ guildId }).sort({ price: 1 });
            const discordId = interaction.user.id;
            if (!roles.length) {
                const embed = await (0, embedBuilder_1.createServerEconomyEmbed)(guildId, 'Role Achievements');
                embed.setDescription(`${embedBuilder_1.EMOJIS.ROLES.MASK} No role achievements are currently available to unlock!\n\nCheck back later or ask an administrator to add some role achievements.`)
                    .setColor(embedBuilder_1.COLORS.INFO);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            const user = await User_2.default.findOne({ discordId, guildId });
            const currentBalance = user?.balance || 0;
            let userAchievements = [];
            if (interaction.guild) {
                const member = await interaction.guild.members.fetch(discordId);
                userAchievements = await (0, roleAssignmentService_1.getUserAchievementRoles)(member);
            }
            const embed = await (0, embedBuilder_1.createServerEconomyEmbed)(guildId, 'Role Achievements');
            embed.setDescription(`${embedBuilder_1.EMOJIS.ROLES.SHIELD} **Role Achievement Progress**\n\nRole achievements are automatically unlocked when you reach the required balance! Your balance represents your lifetime achievements and is never spent.`)
                .setThumbnail('https://cdn.discordapp.com/emojis/1234567890123456789.png');
            const formattedBalance = await (0, embedBuilder_1.formatServerCoins)(guildId, currentBalance);
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.ECONOMY.MONEY} Your Current Balance`,
                value: formattedBalance,
                inline: true
            });
            if (userAchievements.length > 0) {
                const achievementValues = await Promise.all(userAchievements.map(async (achievement) => {
                    const formattedPrice = await (0, embedBuilder_1.formatServerCoins)(guildId, achievement.price);
                    return `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **${achievement.roleName}** - ${formattedPrice}`;
                }));
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.SUCCESS.CROWN} Unlocked Achievements (${userAchievements.length})`,
                    value: achievementValues.join('\n'),
                    inline: false
                });
            }
            const availableRoles = roles.filter(role => !userAchievements.some(achievement => achievement.roleId === role.roleId));
            if (availableRoles.length > 0) {
                const roleFields = await Promise.all(availableRoles.map(async (role) => {
                    const canUnlock = currentBalance >= role.price;
                    const statusEmoji = canUnlock ? embedBuilder_1.EMOJIS.SUCCESS.STAR : embedBuilder_1.EMOJIS.MISC.CLOCK;
                    const formattedPrice = await (0, embedBuilder_1.formatServerCoins)(guildId, role.price);
                    let statusText = '**Ready to unlock!**';
                    if (!canUnlock) {
                        const neededAmount = await (0, embedBuilder_1.formatServerCoins)(guildId, role.price - currentBalance);
                        statusText = `Need ${neededAmount} more`;
                    }
                    return `${statusEmoji} **${role.name}** - ${formattedPrice}\n${statusText}`;
                }));
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} Available Achievements (${availableRoles.length})`,
                    value: roleFields.join('\n\n'),
                    inline: false
                });
            }
            const totalRoles = roles.length;
            const unlockedCount = userAchievements.length;
            const progressPercentage = Math.round((unlockedCount / totalRoles) * 100);
            const summaryBalance = await (0, embedBuilder_1.formatServerCoins)(guildId, currentBalance);
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Achievement Progress`,
                value: `**${unlockedCount}/${totalRoles}** achievements unlocked (${progressPercentage}%)\n` +
                    `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Your Balance: **${summaryBalance}**`,
                inline: false
            });
            embed.setFooter({
                text: 'Role achievements are automatically unlocked when you reach the required balance!'
            });
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            const actionButtons = (0, embedBuilder_1.createQuickActionButtons)();
            const components = [actionButtons];
            await interaction.reply({
                embeds: [embed],
                components: components,
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(error.message);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to fetch roles.');
            }
        }
    })
};
